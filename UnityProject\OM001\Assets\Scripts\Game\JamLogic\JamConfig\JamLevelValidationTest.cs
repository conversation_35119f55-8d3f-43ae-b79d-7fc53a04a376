using UnityEngine;
using Sirenix.OdinInspector;

namespace GameWish.Game
{
    /// <summary>
    /// 关卡验证测试脚本
    /// 用于测试车辆长度验证和占用格子的功能
    /// </summary>
    public class JamLevelValidationTest : MonoBehaviour
    {
        [Title("测试配置")]
        [SerializeField]
        private JamLevelConfig testConfig;

        [Title("车辆放置测试")]
        [SerializeField, Range(0, 19)]
        private int testRow = 2;
        
        [SerializeField, Range(0, 19)]
        private int testCol = 2;
        
        [SerializeField]
        private JamDirection testDirection = JamDirection.Right;
        
        [SerializeField, Range(1, 4)]
        private int testLength = 2;

        [But<PERSON>("测试车辆放置", ButtonSizes.Large)]
        private void TestCarPlacement()
        {
            if (testConfig == null)
            {
                Debug.LogError("请先分配测试配置");
                return;
            }

            if (testConfig.map == null)
            {
                Debug.LogError("测试配置的地图为空");
                return;
            }

            Debug.Log($"测试在位置 ({testRow}, {testCol}) 放置长度为 {testLength} 的车辆，方向：{testDirection}");

            // 检查是否可以放置
            if (CanPlaceCarAt(testRow, testCol, testDirection, testLength, out string errorMessage))
            {
                Debug.Log("✓ 车辆可以放置在此位置");
                
                // 显示车辆将占用的格子
                var occupiedCells = GetCarOccupiedCells(testRow, testCol, testDirection, testLength);
                Debug.Log($"车辆将占用以下格子：");
                for (int i = 0; i < occupiedCells.Count; i++)
                {
                    var (row, col) = occupiedCells[i];
                    string cellType = i == 0 ? "车头" : "车体";
                    Debug.Log($"  - ({row}, {col}) - {cellType}");
                }
            }
            else
            {
                Debug.LogWarning($"✗ 无法放置车辆：{errorMessage}");
            }
        }

        [Button("实际放置车辆", ButtonSizes.Large)]
        private void PlaceCarAtTestPosition()
        {
            if (testConfig == null || testConfig.map == null)
            {
                Debug.LogError("测试配置无效");
                return;
            }

            if (CanPlaceCarAt(testRow, testCol, testDirection, testLength, out string errorMessage))
            {
                // 清除车辆将要占用的所有格子
                ClearCarOccupiedCells(testRow, testCol, testDirection, testLength);
                
                // 放置车头
                testConfig.map[testRow, testCol] = new JamPosData
                {
                    posType = JamPosType.Car,
                    carDirection = testDirection,
                    length = testLength
                };

                Debug.Log($"车辆已放置在位置 ({testRow}, {testCol})");
                
#if UNITY_EDITOR
                UnityEditor.EditorUtility.SetDirty(testConfig);
#endif
            }
            else
            {
                Debug.LogWarning($"无法放置车辆：{errorMessage}");
            }
        }

        [Button("清除测试位置", ButtonSizes.Medium)]
        private void ClearTestPosition()
        {
            if (testConfig == null || testConfig.map == null)
            {
                Debug.LogError("测试配置无效");
                return;
            }

            var cellData = testConfig.map[testRow, testCol];
            if (cellData.posType == JamPosType.Car)
            {
                ClearCarOccupiedCells(testRow, testCol, cellData.carDirection, cellData.length);
            }
            
            testConfig.map[testRow, testCol] = new JamPosData { posType = JamPosType.Empty };
            Debug.Log($"位置 ({testRow}, {testCol}) 已清除");
            
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(testConfig);
#endif
        }

        [Title("地图分析")]
        [Button("分析当前地图", ButtonSizes.Large)]
        private void AnalyzeCurrentMap()
        {
            if (testConfig == null || testConfig.map == null)
            {
                Debug.LogError("测试配置无效");
                return;
            }

            Debug.Log("=== 地图分析结果 ===");
            Debug.Log($"地图尺寸：{testConfig.rows} x {testConfig.cols}");

            int carCount = 0;
            int occupiedCellCount = 0;

            // 统计车辆和占用格子
            for (int row = 0; row < testConfig.rows; row++)
            {
                for (int col = 0; col < testConfig.cols; col++)
                {
                    var cellData = testConfig.map[row, col];
                    if (cellData.posType == JamPosType.Car)
                    {
                        carCount++;
                        var occupiedCells = GetCarOccupiedCells(row, col, cellData.carDirection, cellData.length);
                        occupiedCellCount += occupiedCells.Count;
                        
                        Debug.Log($"车辆 #{carCount} 在 ({row}, {col})，方向：{cellData.carDirection}，长度：{cellData.length}");
                        Debug.Log($"  占用格子：{string.Join(", ", occupiedCells)}");
                    }
                }
            }

            Debug.Log($"总车辆数：{carCount}");
            Debug.Log($"总占用格子数：{occupiedCellCount}");
        }

        // 辅助方法（从编辑器代码复制）
        private bool CanPlaceCarAt(int headRow, int headCol, JamDirection direction, int length, out string errorMessage)
        {
            errorMessage = "";

            if (headRow < 0 || headRow >= testConfig.rows || headCol < 0 || headCol >= testConfig.cols)
            {
                errorMessage = "车头位置超出地图范围";
                return false;
            }

            var occupiedCells = GetCarOccupiedCells(headRow, headCol, direction, length);

            foreach (var (row, col) in occupiedCells)
            {
                if (row < 0 || row >= testConfig.rows || col < 0 || col >= testConfig.cols)
                {
                    errorMessage = $"车辆长度 {length} 超出地图边界";
                    return false;
                }

                var cellData = testConfig.map[row, col];
                if (cellData.posType == JamPosType.Lock)
                {
                    errorMessage = $"位置 ({row}, {col}) 被锁定，无法放置车辆";
                    return false;
                }

                if (cellData.posType == JamPosType.Car && !(row == headRow && col == headCol))
                {
                    errorMessage = $"位置 ({row}, {col}) 已被其他车辆占用";
                    return false;
                }
            }

            return true;
        }

        private System.Collections.Generic.List<(int row, int col)> GetCarOccupiedCells(int headRow, int headCol, JamDirection direction, int length)
        {
            var cells = new System.Collections.Generic.List<(int, int)>();
            
            for (int i = 0; i < length; i++)
            {
                int row = headRow;
                int col = headCol;

                switch (direction)
                {
                    case JamDirection.Up:
                        row = headRow - i;
                        break;
                    case JamDirection.Down:
                        row = headRow + i;
                        break;
                    case JamDirection.Left:
                        col = headCol - i;
                        break;
                    case JamDirection.Right:
                        col = headCol + i;
                        break;
                }

                cells.Add((row, col));
            }

            return cells;
        }

        private void ClearCarOccupiedCells(int headRow, int headCol, JamDirection direction, int length)
        {
            var occupiedCells = GetCarOccupiedCells(headRow, headCol, direction, length);

            for (int i = 1; i < occupiedCells.Count; i++)
            {
                var (row, col) = occupiedCells[i];
                if (row >= 0 && row < testConfig.rows && col >= 0 && col < testConfig.cols)
                {
                    testConfig.map[row, col] = new JamPosData { posType = JamPosType.Empty };
                }
            }
        }
    }
}
