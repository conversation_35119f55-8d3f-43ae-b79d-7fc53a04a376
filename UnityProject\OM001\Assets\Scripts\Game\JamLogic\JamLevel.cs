using System.Collections.Generic;
using Qarth;
using Sirenix.OdinInspector;
using UnityEngine;
using Vector2Int = UnityEngine.Vector2Int;


namespace GameWish.Game
{
	public class JamLevel : MonoBehaviour
	{
		public JamLevelConfig levelConfig;
		public JamPosData[,] grid; // 棋盘网格，0表示空，其他数字表示车辆ID
		public List<JamCar> cars;

		public JamCar preCar2;
		public JamCar preCar3;
		public float xInterval = 1f;
		public float zInterval = 1f;
		[Button("初始化")]
		void InitGameMap()
		{
			//初始化地图数据
			grid = new JamPosData[levelConfig.rows, levelConfig.cols];
			for (int i = 0; i < grid.GetLength(0); i++)
			{
				for (int j = 0; j < grid.GetLength(1); j++)
				{
					grid[i, j] = levelConfig.map[i, j];
				}
			}
			//根据数据设置场景摆放
			cars = new List<JamCar>();
			for (int i = 0; i < grid.GetLength(0); i++)
			{
				for (int j = 0; j < grid.GetLength(1); j++)
				{
					if (grid[i, j].posType == JamPosType.Car)
					{
						//根据长度 方向 生成车辆
						// var car = Instantiate(preCar, transform);
					}
				}
			}


		}

		public bool CanMoveCar(JamCar car, out List<Vector2Int> lstCheckPos, out Vector2Int finalPos)
		{
			return CanMoveCar(car.x, car.y, car.direction, out lstCheckPos, out finalPos);
		}

		public bool CanMoveCar(int x, int y, JamDirection direction, out List<Vector2Int> lstCheckPos, out Vector2Int finalPos)
		{
			var data = grid[x, y];
			//当前朝向直到边缘都是空的
			lstCheckPos = new List<Vector2Int>();
			switch (direction)
			{
				case JamDirection.Up:
					for (int i = y - 1; i >= 0; i--)
					{
						lstCheckPos.Add(new Vector2Int(x, i));
					}
					break;
				case JamDirection.Down:
					for (int i = y + 1; i < levelConfig.rows; i++)
					{
						lstCheckPos.Add(new Vector2Int(x, i));
					}
					break;
				case JamDirection.Left:
					for (int i = x - 1; i >= 0; i--)
					{
						lstCheckPos.Add(new Vector2Int(i, y));
					}
					break;
				case JamDirection.Right:
					for (int i = x + 1; i < levelConfig.cols; i++)
					{
						lstCheckPos.Add(new Vector2Int(i, y));
					}
					break;
			}
			foreach (var pos in lstCheckPos)
			{
				if (grid[pos.x, pos.y].posType != JamPosType.Empty)
				{
					finalPos = pos;
					return false;
				}
			}
			finalPos = lstCheckPos[lstCheckPos.Count - 1];
			return true;
		}

	}

}