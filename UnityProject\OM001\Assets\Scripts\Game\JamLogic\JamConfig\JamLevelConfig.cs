using UnityEngine;
using Sirenix.OdinInspector;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace GameWish.Game
{
	[CreateAssetMenu(fileName = "JamLevelConfig", menuName = "创建Jam关卡配置")]
	public class JamLevelConfig : ScriptableObject
	{
		[Title("关卡基本信息")]
		[LabelText("关卡目标")]
		public int levelTarget = 10;

		[Title("地图设置")]
		[HorizontalGroup("MapSize")]
		[LabelText("行数"), Range(3, 20)]
		[OnValueChanged("UpdateMapSize")]
		public int rows = 6;

		[HorizontalGroup("MapSize")]
		[LabelText("列数"), Range(3, 20)]
		[OnValueChanged("UpdateMapSize")]
		public int cols = 6;

		[Title("地图编辑器")]
		[InfoBox("使用下面的工具来编辑地图。点击格子可以切换状态。", InfoMessageType.Info)]

		[HorizontalGroup("Tools")]
		[Button("清空地图", ButtonSizes.Medium)]
		private void ClearMap()
		{
			if (map != null)
			{
				for (int i = 0; i < rows; i++)
				{
					for (int j = 0; j < cols; j++)
					{
						map[i, j] = new JamPosData { posType = JamPosType.Empty };
					}
				}
			}
		}

		[HorizontalGroup("Tools")]
		[Button("填充空地", ButtonSizes.Medium)]
		private void FillEmpty()
		{
			if (map != null)
			{
				for (int i = 0; i < rows; i++)
				{
					for (int j = 0; j < cols; j++)
					{
						if (map[i, j].posType == JamPosType.Lock)
							map[i, j] = new JamPosData { posType = JamPosType.Empty };
					}
				}
			}
		}

		[HorizontalGroup("Tools")]
		[Button("随机生成", ButtonSizes.Medium)]
		private void RandomGenerate()
		{
			if (map != null)
			{
				for (int i = 0; i < rows; i++)
				{
					for (int j = 0; j < cols; j++)
					{
						var randomType = (JamPosType)Random.Range(-1, 3);
						var randomDirection = (JamDirection)Random.Range(0, 4);
						var randomLength = Random.Range(1, 4);

						map[i, j] = new JamPosData
						{
							posType = randomType,
							carDirection = randomDirection,
							length = randomLength
						};
					}
				}
			}
		}

		[Space(10)]
		[TableMatrix(
			SquareCells = true,
			DrawElementMethod = "DrawMapElement",
			ResizableColumns = false,
			HideColumnIndices = true,
			HideRowIndices = true
		)]
		public JamPosData[,] map;

		[Title("保存和加载")]
		[HorizontalGroup("SaveLoad")]
		[Button("保存到JSON", ButtonSizes.Large)]
		private void SaveToJson()
		{
#if UNITY_EDITOR
			string path = EditorUtility.SaveFilePanel("保存关卡配置", Application.dataPath, name + ".json", "json");
			if (!string.IsNullOrEmpty(path))
			{
				var levelData = new JamLevelData
				{
					levelTarget = levelTarget,
					rows = rows,
					cols = cols,
					mapData = new JamPosData[rows * cols]
				};

				// 将二维数组转换为一维数组
				for (int i = 0; i < rows; i++)
				{
					for (int j = 0; j < cols; j++)
					{
						levelData.mapData[i * cols + j] = map[i, j];
					}
				}

				string json = JsonUtility.ToJson(levelData, true);
				File.WriteAllText(path, json);
				Debug.Log($"关卡配置已保存到: {path}");
			}
#endif
		}

		[HorizontalGroup("SaveLoad")]
		[Button("从JSON加载", ButtonSizes.Large)]
		private void LoadFromJson()
		{
#if UNITY_EDITOR
			string path = EditorUtility.OpenFilePanel("加载关卡配置", Application.dataPath, "json");
			if (!string.IsNullOrEmpty(path))
			{
				string json = File.ReadAllText(path);
				var levelData = JsonUtility.FromJson<JamLevelData>(json);

				levelTarget = levelData.levelTarget;
				rows = levelData.rows;
				cols = levelData.cols;

				UpdateMapSize();

				// 将一维数组转换为二维数组
				for (int i = 0; i < rows; i++)
				{
					for (int j = 0; j < cols; j++)
					{
						if (i * cols + j < levelData.mapData.Length)
						{
							map[i, j] = levelData.mapData[i * cols + j];
						}
					}
				}

				Debug.Log($"关卡配置已从 {path} 加载");
			}
#endif
		}

		private void UpdateMapSize()
		{
			// 保存旧数据
			JamPosData[,] oldMap = map;
			int oldRows = oldMap?.GetLength(0) ?? 0;
			int oldCols = oldMap?.GetLength(1) ?? 0;

			// 创建新地图
			map = new JamPosData[rows, cols];

			// 复制旧数据到新地图
			if (oldMap != null)
			{
				for (int i = 0; i < Mathf.Min(rows, oldRows); i++)
				{
					for (int j = 0; j < Mathf.Min(cols, oldCols); j++)
					{
						map[i, j] = oldMap[i, j];
					}
				}
			}

			// 初始化新区域
			for (int i = 0; i < rows; i++)
			{
				for (int j = 0; j < cols; j++)
				{
					if (i >= oldRows || j >= oldCols)
					{
						map[i, j] = new JamPosData { posType = JamPosType.Empty };
					}
				}
			}
		}

#if UNITY_EDITOR
		private JamPosData DrawMapElement(Rect rect, JamPosData value)
		{
			// 根据类型设置颜色
			Color backgroundColor = GetColorForPosType(value.posType);
			EditorGUI.DrawRect(rect, backgroundColor);

			// 绘制边框
			EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, 1), Color.black);
			EditorGUI.DrawRect(new Rect(rect.x, rect.y + rect.height - 1, rect.width, 1), Color.black);
			EditorGUI.DrawRect(new Rect(rect.x, rect.y, 1, rect.height), Color.black);
			EditorGUI.DrawRect(new Rect(rect.x + rect.width - 1, rect.y, 1, rect.height), Color.black);

			// 绘制文本信息
			GUIStyle style = new GUIStyle(GUI.skin.label)
			{
				alignment = TextAnchor.MiddleCenter,
				fontSize = 8,
				normal = { textColor = Color.black }
			};

			string displayText = GetDisplayTextForPosType(value.posType);
			if (value.posType == JamPosType.Car)
			{
				displayText = "车头\n" + displayText + $"\nL:{value.length}\n{GetDirectionSymbol(value.carDirection)}";
			}

			GUI.Label(rect, displayText, style);

			// 处理点击事件
			if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
			{
				// 验证车辆放置的合法性
				if (value.posType == JamPosType.Car)
				{
					var newValue = CycleToNextPosType(value);
					if (newValue.posType == JamPosType.Car && !IsValidCarPlacement(newValue))
					{
						Debug.LogWarning("车辆长度或方向设置不合法，无法放置");
						return value; // 不改变原值
					}
					value = newValue;
				}
				else
				{
					value = CycleToNextPosType(value);
				}
				Event.current.Use();
			}

			return value;
		}

		private bool IsValidCarPlacement(JamPosData carData)
		{
			// 这里可以添加车辆放置的验证逻辑
			// 由于在Inspector中我们无法获取当前格子的具体位置，
			// 所以这里只做基本的数据验证
			return carData.length >= 1 && carData.length <= 4;
		}

		private Color GetColorForPosType(JamPosType posType)
		{
			switch (posType)
			{
				case JamPosType.Empty: return Color.white;
				case JamPosType.Car: return Color.green;
				case JamPosType.Lock: return Color.red;
				case JamPosType.Undergroud: return Color.blue;
				default: return Color.gray;
			}
		}

		private string GetDisplayTextForPosType(JamPosType posType)
		{
			switch (posType)
			{
				case JamPosType.Empty: return "空";
				case JamPosType.Car: return "车";
				case JamPosType.Lock: return "锁";
				case JamPosType.Undergroud: return "地下";
				default: return "?";
			}
		}

		private string GetDirectionSymbol(JamDirection direction)
		{
			switch (direction)
			{
				case JamDirection.Up: return "↑";
				case JamDirection.Down: return "↓";
				case JamDirection.Left: return "←";
				case JamDirection.Right: return "→";
				default: return "?";
			}
		}

		private JamPosData CycleToNextPosType(JamPosData current)
		{
			// 循环切换状态
			switch (current.posType)
			{
				case JamPosType.Empty:
					return new JamPosData
					{
						posType = JamPosType.Car,
						length = 2,
						carDirection = JamDirection.Right
					};
				case JamPosType.Car:
					// 如果是车辆，先循环方向，再循环长度，最后切换到锁定
					if (current.carDirection == JamDirection.Right)
						return new JamPosData { posType = JamPosType.Car, length = current.length, carDirection = JamDirection.Down };
					else if (current.carDirection == JamDirection.Down)
						return new JamPosData { posType = JamPosType.Car, length = current.length, carDirection = JamDirection.Left };
					else if (current.carDirection == JamDirection.Left)
						return new JamPosData { posType = JamPosType.Car, length = current.length, carDirection = JamDirection.Up };
					else if (current.carDirection == JamDirection.Up)
					{
						if (current.length < 4)
							return new JamPosData { posType = JamPosType.Car, length = current.length + 1, carDirection = JamDirection.Right };
						else
							return new JamPosData { posType = JamPosType.Lock };
					}
					break;
				case JamPosType.Lock:
					return new JamPosData { posType = JamPosType.Undergroud };
				case JamPosType.Undergroud:
					return new JamPosData { posType = JamPosType.Empty };
			}
			return current;
		}
#endif
	}

	[System.Serializable]
	public class JamLevelData
	{
		public int levelTarget;
		public int rows;
		public int cols;
		public JamPosData[] mapData;
	}
}