using System;
using UnityEngine;


namespace GameWish.Game
{
	public class JamDefine
	{

	}

	public enum JamDirection
	{
		Up,
		Down,
		Left,
		Right
	}

	public enum JamPosType
	{
		Lock = -1,
		Empty = 0,
		Car = 1,
		Undergroud = 2,//地下停车场
	}


	public enum JamCarType
	{
		Type1 = 1,
		Type2 = 2,
		Type3 = 3,
		Type4,
		Type5,
		Type6,
		Type7,
		Type8,

	}

	[Serializable]
	public struct JamPosData
	{
		public JamPosType posType;
		public int length;
		public JamDirection carDirection;
	}
}