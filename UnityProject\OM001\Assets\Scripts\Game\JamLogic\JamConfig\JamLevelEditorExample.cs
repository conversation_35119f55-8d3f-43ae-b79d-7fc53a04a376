using UnityEngine;
using Sirenix.OdinInspector;

namespace GameWish.Game
{
    /// <summary>
    /// Jam关卡编辑器使用示例
    /// 这个类展示了如何在代码中操作关卡配置
    /// </summary>
    public class JamLevelEditorExample : MonoBehaviour
    {
        [Title("关卡配置示例")]
        [InfoBox("这个示例展示了如何在代码中创建和操作关卡配置", InfoMessageType.Info)]
        
        [SerializeField]
        private JamLevelConfig exampleConfig;
        
        [SerializeField]
        private JamLevelTemplate exampleTemplate;

        [Title("运行时操作")]
        [Button("创建示例关卡", ButtonSizes.Large)]
        private void CreateExampleLevel()
        {
            if (exampleConfig == null)
            {
                Debug.LogWarning("请先分配一个关卡配置");
                return;
            }

            // 设置基本信息
            exampleConfig.levelTarget = 20;
            exampleConfig.rows = 6;
            exampleConfig.cols = 6;

            // 初始化地图
            exampleConfig.map = new JamPosData[6, 6];
            
            // 清空地图
            for (int i = 0; i < 6; i++)
            {
                for (int j = 0; j < 6; j++)
                {
                    exampleConfig.map[i, j] = new JamPosData { posType = JamPosType.Empty };
                }
            }

            // 添加一些车辆
            exampleConfig.map[1, 1] = new JamPosData 
            { 
                posType = JamPosType.Car, 
                carDirection = JamDirection.Right, 
                length = 2 
            };
            
            exampleConfig.map[2, 3] = new JamPosData 
            { 
                posType = JamPosType.Car, 
                carDirection = JamDirection.Down, 
                length = 3 
            };
            
            exampleConfig.map[4, 0] = new JamPosData 
            { 
                posType = JamPosType.Car, 
                carDirection = JamDirection.Right, 
                length = 2 
            };

            // 添加障碍物
            exampleConfig.map[0, 0] = new JamPosData { posType = JamPosType.Lock };
            exampleConfig.map[5, 5] = new JamPosData { posType = JamPosType.Lock };
            exampleConfig.map[2, 2] = new JamPosData { posType = JamPosType.Undergroud };

            Debug.Log("示例关卡创建完成！");
        }

        [Button("应用模板到关卡", ButtonSizes.Large)]
        private void ApplyTemplateToLevel()
        {
            if (exampleTemplate == null || exampleConfig == null)
            {
                Debug.LogWarning("请先分配模板和关卡配置");
                return;
            }

            exampleTemplate.ApplyTemplateToConfig(exampleConfig);
            Debug.Log($"模板 {exampleTemplate.templateName} 已应用到关卡配置");
        }

        [Button("验证关卡数据", ButtonSizes.Large)]
        private void ValidateLevelData()
        {
            if (exampleConfig == null || exampleConfig.map == null)
            {
                Debug.LogWarning("关卡配置或地图数据为空");
                return;
            }

            int carCount = 0;
            int lockCount = 0;
            int undergroundCount = 0;
            int emptyCount = 0;

            for (int i = 0; i < exampleConfig.rows; i++)
            {
                for (int j = 0; j < exampleConfig.cols; j++)
                {
                    switch (exampleConfig.map[i, j].posType)
                    {
                        case JamPosType.Car:
                            carCount++;
                            break;
                        case JamPosType.Lock:
                            lockCount++;
                            break;
                        case JamPosType.Undergroud:
                            undergroundCount++;
                            break;
                        case JamPosType.Empty:
                            emptyCount++;
                            break;
                    }
                }
            }

            Debug.Log($"关卡数据统计：");
            Debug.Log($"- 地图尺寸: {exampleConfig.rows}x{exampleConfig.cols}");
            Debug.Log($"- 关卡目标: {exampleConfig.levelTarget}");
            Debug.Log($"- 车辆数量: {carCount}");
            Debug.Log($"- 锁定区域: {lockCount}");
            Debug.Log($"- 地下停车场: {undergroundCount}");
            Debug.Log($"- 空地数量: {emptyCount}");
        }

        [Title("工具方法")]
        [Button("打印地图到控制台", ButtonSizes.Medium)]
        private void PrintMapToConsole()
        {
            if (exampleConfig == null || exampleConfig.map == null)
            {
                Debug.LogWarning("关卡配置或地图数据为空");
                return;
            }

            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            sb.AppendLine($"关卡地图 ({exampleConfig.rows}x{exampleConfig.cols}):");
            sb.AppendLine("=".PadRight(exampleConfig.cols * 4, '='));

            for (int i = 0; i < exampleConfig.rows; i++)
            {
                for (int j = 0; j < exampleConfig.cols; j++)
                {
                    string symbol = GetSymbolForPosType(exampleConfig.map[i, j].posType);
                    sb.Append($"{symbol,3} ");
                }
                sb.AppendLine();
            }

            Debug.Log(sb.ToString());
        }

        private string GetSymbolForPosType(JamPosType posType)
        {
            return posType switch
            {
                JamPosType.Empty => "□",
                JamPosType.Car => "■",
                JamPosType.Lock => "×",
                JamPosType.Undergroud => "○",
                _ => "?"
            };
        }

        [Title("JSON操作示例")]
        [Button("导出为JSON字符串", ButtonSizes.Medium)]
        private void ExportToJsonString()
        {
            if (exampleConfig == null || exampleConfig.map == null)
            {
                Debug.LogWarning("关卡配置或地图数据为空");
                return;
            }

            var levelData = new JamLevelData
            {
                levelTarget = exampleConfig.levelTarget,
                rows = exampleConfig.rows,
                cols = exampleConfig.cols,
                mapData = new JamPosData[exampleConfig.rows * exampleConfig.cols]
            };

            // 将二维数组转换为一维数组
            for (int i = 0; i < exampleConfig.rows; i++)
            {
                for (int j = 0; j < exampleConfig.cols; j++)
                {
                    levelData.mapData[i * exampleConfig.cols + j] = exampleConfig.map[i, j];
                }
            }

            string json = JsonUtility.ToJson(levelData, true);
            Debug.Log($"关卡JSON数据：\n{json}");
        }

        [Title("性能测试")]
        [Button("批量创建关卡测试", ButtonSizes.Medium)]
        private void BatchCreateLevelsTest()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            for (int level = 0; level < 100; level++)
            {
                var testMap = new JamPosData[6, 6];
                
                for (int i = 0; i < 6; i++)
                {
                    for (int j = 0; j < 6; j++)
                    {
                        // 随机生成地图内容
                        var randomType = (JamPosType)Random.Range(-1, 3);
                        testMap[i, j] = new JamPosData 
                        { 
                            posType = randomType,
                            carDirection = (JamDirection)Random.Range(0, 4),
                            length = Random.Range(1, 5)
                        };
                    }
                }
            }
            
            stopwatch.Stop();
            Debug.Log($"批量创建100个6x6关卡用时: {stopwatch.ElapsedMilliseconds}ms");
        }
    }
}
