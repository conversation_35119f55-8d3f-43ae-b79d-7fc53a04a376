# Jam关卡编辑器使用说明

## 概述
这是一个基于OdinInspector的关卡编辑器，用于创建和编辑Jam游戏的关卡配置。编辑器提供了直观的可视化界面，支持地图编辑、模板系统和文件导入导出功能。

## 主要功能

### 1. 关卡配置编辑 (JamLevelConfig)
- **基本信息设置**：关卡目标、地图尺寸
- **可视化地图编辑**：点击格子切换状态
- **实时预览**：不同颜色表示不同的格子类型
- **数据保存**：自动保存到ScriptableObject资源文件

### 2. 编辑器窗口 (JamLevelEditor)
通过菜单 `Tools -> Jam关卡编辑器` 打开独立的编辑器窗口。

#### 主要特性：
- **配置管理**：创建新配置或选择现有配置
- **绘制工具**：选择要绘制的格子类型、方向和长度
- **地图编辑**：
  - 左键点击：设置格子为选中类型
  - 右键点击：清空格子
- **快捷操作**：清空地图、填充空地、随机生成
- **文件操作**：导出/导入JSON格式的关卡数据

### 3. 模板系统 (JamLevelTemplate)
预设的关卡模板，可以快速创建常见的关卡布局。

#### 内置模板：
- **空白模板**：全空的地图
- **简单关卡模板**：包含基本车辆和障碍的6x6地图
- **复杂关卡模板**：带边界锁定的8x8复杂布局

## 格子类型说明

| 类型 | 显示 | 颜色 | 说明 |
|------|------|------|------|
| Empty | 空 | 白色 | 空地，可以通行 |
| Car | 车 | 绿色 | 车辆，有方向和长度属性 |
| Lock | 锁 | 红色 | 锁定区域，不可通行 |
| Underground | 地下 | 蓝色 | 地下停车场 |

## 使用流程

### 创建新关卡
1. 在Project窗口右键 -> Create -> 创建Jam关卡配置
2. 选中创建的配置文件，在Inspector中编辑
3. 或者打开编辑器窗口进行可视化编辑

### 使用编辑器窗口
1. 菜单栏选择 `Tools -> Jam关卡编辑器`
2. 创建新配置或选择现有配置
3. 设置地图尺寸和关卡目标
4. 选择绘制工具（格子类型、方向、长度）
5. 在地图网格中点击编辑
6. 保存配置

### 使用模板
1. 在Project窗口右键 -> Create -> 创建Jam关卡模板
2. 选择预设模板或自定义编辑
3. 在编辑器窗口中选择模板并应用到关卡配置

### 导入导出
- **导出JSON**：将关卡数据导出为JSON文件，便于版本控制和分享
- **导入JSON**：从JSON文件导入关卡数据

## 车辆属性说明

### 方向 (JamDirection)
- **Up (↑)**：向上
- **Down (↓)**：向下  
- **Left (←)**：向左
- **Right (→)**：向右

### 长度 (Length)
车辆占用的格子数量，范围1-4

## 技术实现

### 核心类
- `JamLevelConfig`：关卡配置ScriptableObject
- `JamLevelEditor`：编辑器窗口
- `JamLevelTemplate`：关卡模板
- `JamPosData`：格子数据结构
- `JamLevelData`：JSON序列化数据结构

### 依赖
- **OdinInspector**：提供强大的Inspector扩展功能
- **Unity Editor**：编辑器相关API

## 扩展建议

1. **撤销/重做功能**：使用Unity的Undo系统
2. **多选编辑**：支持框选多个格子批量编辑
3. **图层系统**：支持背景、车辆、装饰等多图层编辑
4. **预览功能**：实时预览关卡在游戏中的效果
5. **验证系统**：检查关卡的可玩性和合理性

## 注意事项

1. 编辑器窗口需要在Unity Editor中使用
2. 确保已安装OdinInspector插件
3. 地图数据使用二维数组存储，注意行列索引
4. JSON导入导出时会进行一维/二维数组转换
5. 修改配置后记得保存资源文件

## 故障排除

### 常见问题
1. **编辑器窗口无法打开**：检查OdinInspector是否正确安装
2. **地图显示异常**：检查地图尺寸设置是否正确
3. **保存失败**：确保有写入权限，检查文件路径
4. **模板应用失败**：检查模板数据是否完整

### 调试建议
- 查看Console窗口的日志信息
- 检查Inspector中的数据是否正确
- 使用Unity的Profiler检查性能问题
