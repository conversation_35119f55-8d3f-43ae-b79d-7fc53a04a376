using System.Collections.Generic;
using UnityEngine;


namespace GameWish.Game
{
	public class JamCar : MonoBehaviour
	{
		public int carId;
		public JamDirection direction;
		public JamCarType carType;
		public int length;
		public int capacity;
		public int x;
		public int y;

		// 检查移动是否合法
		public bool CanMove(out List<Vector2Int> lstCheckPos, out Vector2Int finalPos)
		{
			return JamGameMgr.S.level.CanMoveCar(x, y, direction, out lstCheckPos, out finalPos);
		}

		// 驶出车辆
		public virtual void Out()
		{
			if (CanMove(out var lstCheckPos, out var finalPos))
			{
				//跟着checkPos移动,移动到finalPos再寻路去目标点 移走了再重置点的状态
			}
			else
			{
				//撞到不支持的CheckPos回弹  finalPos
			}
		}

	}
}