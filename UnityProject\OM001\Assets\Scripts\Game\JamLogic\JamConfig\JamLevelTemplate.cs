using UnityEngine;
using Sirenix.OdinInspector;

namespace GameWish.Game
{
    [CreateAssetMenu(fileName = "JamLevelTemplate", menuName = "创建Jam关卡模板")]
    public class JamLevelTemplate : ScriptableObject
    {
        [Title("模板信息")]
        [LabelText("模板名称")]
        public string templateName = "新模板";
        
        [LabelText("模板描述")]
        [TextArea(2, 4)]
        public string description = "描述这个模板的特点和用途";
        
        [Title("模板数据")]
        [LabelText("关卡目标")]
        public int levelTarget = 10;
        
        [LabelText("地图行数")]
        [Range(3, 20)]
        public int rows = 6;
        
        [LabelText("地图列数")]
        [Range(3, 20)]
        public int cols = 6;
        
        [TableMatrix(SquareCells = true, DrawElementMethod = "DrawTemplateElement")]
        public JamPosData[,] templateMap;
        
        [Title("预设模板")]
        [Button("创建空白模板", ButtonSizes.Large)]
        private void CreateEmptyTemplate()
        {
            templateMap = new JamPosData[rows, cols];
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    templateMap[i, j] = new JamPosData { posType = JamPosType.Empty };
                }
            }
        }
        
        [Button("创建简单关卡模板", ButtonSizes.Large)]
        private void CreateSimpleTemplate()
        {
            rows = 6;
            cols = 6;
            templateMap = new JamPosData[rows, cols];
            
            // 创建一个简单的关卡布局
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    templateMap[i, j] = new JamPosData { posType = JamPosType.Empty };
                }
            }
            
            // 添加一些车辆
            templateMap[2, 1] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Right, length = 2 };
            templateMap[3, 2] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Down, length = 3 };
            templateMap[1, 4] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Left, length = 2 };
            
            // 添加一些障碍
            templateMap[0, 0] = new JamPosData { posType = JamPosType.Lock };
            templateMap[5, 5] = new JamPosData { posType = JamPosType.Lock };
            templateMap[2, 4] = new JamPosData { posType = JamPosType.Undergroud };
        }
        
        [Button("创建复杂关卡模板", ButtonSizes.Large)]
        private void CreateComplexTemplate()
        {
            rows = 8;
            cols = 8;
            templateMap = new JamPosData[rows, cols];
            
            // 创建一个复杂的关卡布局
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    templateMap[i, j] = new JamPosData { posType = JamPosType.Empty };
                }
            }
            
            // 添加边界锁定
            for (int i = 0; i < rows; i++)
            {
                templateMap[i, 0] = new JamPosData { posType = JamPosType.Lock };
                templateMap[i, cols - 1] = new JamPosData { posType = JamPosType.Lock };
            }
            for (int j = 0; j < cols; j++)
            {
                templateMap[0, j] = new JamPosData { posType = JamPosType.Lock };
                templateMap[rows - 1, j] = new JamPosData { posType = JamPosType.Lock };
            }
            
            // 添加内部车辆和障碍
            templateMap[2, 2] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Right, length = 3 };
            templateMap[3, 1] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Down, length = 2 };
            templateMap[4, 4] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Left, length = 2 };
            templateMap[5, 3] = new JamPosData { posType = JamPosType.Car, carDirection = JamDirection.Up, length = 3 };
            
            // 添加地下停车场
            templateMap[3, 5] = new JamPosData { posType = JamPosType.Undergroud };
            templateMap[5, 2] = new JamPosData { posType = JamPosType.Undergroud };
        }
        
        [Title("应用模板")]
        [Button("应用到选中的关卡配置", ButtonSizes.Large)]
        private void ApplyToSelectedConfig()
        {
#if UNITY_EDITOR
            var selectedObjects = UnityEditor.Selection.objects;
            foreach (var obj in selectedObjects)
            {
                if (obj is JamLevelConfig config)
                {
                    ApplyTemplateToConfig(config);
                    UnityEditor.EditorUtility.SetDirty(config);
                    Debug.Log($"模板已应用到 {config.name}");
                }
            }
            
            if (selectedObjects.Length == 0)
            {
                Debug.LogWarning("请先在Project窗口中选择一个或多个JamLevelConfig文件");
            }
#endif
        }
        
        public void ApplyTemplateToConfig(JamLevelConfig config)
        {
            if (templateMap == null)
            {
                Debug.LogWarning("模板地图为空，请先创建模板");
                return;
            }
            
            config.levelTarget = levelTarget;
            config.rows = rows;
            config.cols = cols;
            
            // 复制模板地图数据
            config.map = new JamPosData[rows, cols];
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    config.map[i, j] = templateMap[i, j];
                }
            }
        }
        
#if UNITY_EDITOR
        private JamPosData DrawTemplateElement(Rect rect, JamPosData value)
        {
            // 根据类型设置颜色
            Color backgroundColor = GetColorForPosType(value.posType);
            UnityEditor.EditorGUI.DrawRect(rect, backgroundColor);

            // 绘制边框
            UnityEditor.EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, 1), Color.black);
            UnityEditor.EditorGUI.DrawRect(new Rect(rect.x, rect.y + rect.height - 1, rect.width, 1), Color.black);
            UnityEditor.EditorGUI.DrawRect(new Rect(rect.x, rect.y, 1, rect.height), Color.black);
            UnityEditor.EditorGUI.DrawRect(new Rect(rect.x + rect.width - 1, rect.y, 1, rect.height), Color.black);

            // 绘制文本信息
            GUIStyle style = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleCenter,
                fontSize = 8,
                normal = { textColor = Color.black }
            };

            string displayText = GetDisplayTextForPosType(value.posType);
            if (value.posType == JamPosType.Car)
            {
                displayText += $"\nL:{value.length}\n{GetDirectionSymbol(value.carDirection)}";
            }

            GUI.Label(rect, displayText, style);

            // 处理点击事件（简单的循环切换）
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                value = CycleToNextPosType(value);
                Event.current.Use();
            }

            return value;
        }

        private Color GetColorForPosType(JamPosType posType)
        {
            return posType switch
            {
                JamPosType.Empty => Color.white,
                JamPosType.Car => Color.green,
                JamPosType.Lock => Color.red,
                JamPosType.Undergroud => Color.blue,
                _ => Color.gray
            };
        }

        private string GetDisplayTextForPosType(JamPosType posType)
        {
            return posType switch
            {
                JamPosType.Empty => "空",
                JamPosType.Car => "车",
                JamPosType.Lock => "锁",
                JamPosType.Undergroud => "地下",
                _ => "?"
            };
        }

        private string GetDirectionSymbol(JamDirection direction)
        {
            return direction switch
            {
                JamDirection.Up => "↑",
                JamDirection.Down => "↓",
                JamDirection.Left => "←",
                JamDirection.Right => "→",
                _ => "?"
            };
        }

        private JamPosData CycleToNextPosType(JamPosData current)
        {
            // 简单的循环切换
            switch (current.posType)
            {
                case JamPosType.Empty:
                    return new JamPosData { posType = JamPosType.Car, length = 2, carDirection = JamDirection.Right };
                case JamPosType.Car:
                    return new JamPosData { posType = JamPosType.Lock };
                case JamPosType.Lock:
                    return new JamPosData { posType = JamPosType.Undergroud };
                case JamPosType.Undergroud:
                    return new JamPosData { posType = JamPosType.Empty };
                default:
                    return current;
            }
        }
#endif
    }
}
