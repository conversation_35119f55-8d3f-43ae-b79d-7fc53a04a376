using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using GameWish.Game;
using System.IO;

namespace GameWish.Game.Editor
{
    public class JamLevelEditor : OdinEditorWindow
    {
        [MenuItem("Tools/Jam关卡编辑器")]
        private static void OpenWindow()
        {
            GetWindow<JamLevelEditor>().Show();
        }

        [SerializeField]
        private JamLevelConfig currentConfig;

        [SerializeField]
        private JamPosType selectedPosType = JamPosType.Empty;

        [SerializeField]
        private JamDirection selectedDirection = JamDirection.Right;

        [SerializeField]
        private int selectedLength = 2;

        [SerializeField]
        private JamLevelTemplate selectedTemplate;

        private Vector2 scrollPosition;

        protected override void OnImGUI()
        {
            base.OnImGUI();

            if (currentConfig == null)
            {
                EditorGUILayout.HelpBox("请先选择或创建一个JamLevelConfig", MessageType.Info);

                if (GUILayout.Button("创建新的关卡配置"))
                {
                    CreateNewLevelConfig();
                }

                if (GUILayout.Button("选择现有配置"))
                {
                    SelectExistingConfig();
                }
                return;
            }

            DrawLevelEditor();
        }

        private void CreateNewLevelConfig()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建新的关卡配置",
                "NewJamLevel",
                "asset",
                "选择保存位置");

            if (!string.IsNullOrEmpty(path))
            {
                var config = CreateInstance<JamLevelConfig>();
                AssetDatabase.CreateAsset(config, path);
                AssetDatabase.SaveAssets();
                currentConfig = config;
            }
        }

        private void SelectExistingConfig()
        {
            string path = EditorUtility.OpenFilePanel("选择关卡配置", Application.dataPath, "asset");
            if (!string.IsNullOrEmpty(path))
            {
                path = "Assets" + path.Substring(Application.dataPath.Length);
                currentConfig = AssetDatabase.LoadAssetAtPath<JamLevelConfig>(path);
            }
        }

        private void DrawLevelEditor()
        {
            EditorGUILayout.BeginVertical();

            // 显示当前配置信息
            EditorGUILayout.LabelField("当前配置", EditorStyles.boldLabel);
            EditorGUILayout.ObjectField("配置文件", currentConfig, typeof(JamLevelConfig), false);

            EditorGUILayout.Space();

            // 基本设置
            EditorGUILayout.LabelField("基本设置", EditorStyles.boldLabel);
            currentConfig.levelTarget = EditorGUILayout.IntField("关卡目标", currentConfig.levelTarget);

            // 地图尺寸
            EditorGUILayout.BeginHorizontal();
            int newRows = EditorGUILayout.IntSlider("行数", currentConfig.rows, 3, 20);
            int newCols = EditorGUILayout.IntSlider("列数", currentConfig.cols, 3, 20);
            EditorGUILayout.EndHorizontal();

            if (newRows != currentConfig.rows || newCols != currentConfig.cols)
            {
                currentConfig.rows = newRows;
                currentConfig.cols = newCols;
                ResizeMap();
            }

            EditorGUILayout.Space();

            // 绘制工具
            EditorGUILayout.LabelField("绘制工具", EditorStyles.boldLabel);
            selectedPosType = (JamPosType)EditorGUILayout.EnumPopup("选择类型", selectedPosType);

            if (selectedPosType == JamPosType.Car)
            {
                selectedDirection = (JamDirection)EditorGUILayout.EnumPopup("车辆方向", selectedDirection);
                selectedLength = EditorGUILayout.IntSlider("车辆长度", selectedLength, 1, 4);

                // 显示车辆放置说明
                EditorGUILayout.HelpBox(
                    $"车辆设置：长度{selectedLength}，方向{GetDirectionName(selectedDirection)}\n" +
                    "点击的位置将作为车头，车辆会向指定方向延伸。\n" +
                    "如果空间不足会显示警告信息。",
                    MessageType.Info);
            }

            EditorGUILayout.Space();

            // 模板功能
            EditorGUILayout.LabelField("模板功能", EditorStyles.boldLabel);
            selectedTemplate = (JamLevelTemplate)EditorGUILayout.ObjectField("选择模板", selectedTemplate, typeof(JamLevelTemplate), false);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("应用模板") && selectedTemplate != null)
            {
                ApplyTemplate();
            }
            if (GUILayout.Button("创建新模板"))
            {
                CreateNewTemplate();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 快捷操作
            EditorGUILayout.LabelField("快捷操作", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("清空地图"))
            {
                ClearMap();
            }
            if (GUILayout.Button("填充空地"))
            {
                FillEmpty();
            }
            if (GUILayout.Button("随机生成"))
            {
                RandomGenerate();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 地图编辑区域
            EditorGUILayout.LabelField("地图编辑", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("左键点击设置格子，右键点击清空格子", MessageType.Info);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            DrawMapGrid();
            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space();

            // 保存和加载
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("保存配置"))
            {
                EditorUtility.SetDirty(currentConfig);
                AssetDatabase.SaveAssets();
                Debug.Log("配置已保存");
            }
            if (GUILayout.Button("导出JSON"))
            {
                ExportToJson();
            }
            if (GUILayout.Button("导入JSON"))
            {
                ImportFromJson();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        private void DrawMapGrid()
        {
            if (currentConfig.map == null)
            {
                ResizeMap();
                return;
            }

            float cellSize = 60f;

            for (int i = 0; i < currentConfig.rows; i++)
            {
                EditorGUILayout.BeginHorizontal();
                for (int j = 0; j < currentConfig.cols; j++)
                {
                    Rect cellRect = GUILayoutUtility.GetRect(cellSize, cellSize);
                    DrawMapCell(cellRect, i, j);
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        private void DrawMapCell(Rect rect, int row, int col)
        {
            var cellData = currentConfig.map[row, col];

            // 检查这个格子是否被车辆占用（但不是车头）
            bool isCarOccupied = IsCarOccupiedCell(row, col);

            // 绘制背景
            Color backgroundColor;
            if (isCarOccupied)
            {
                backgroundColor = Color.Lerp(Color.green, Color.white, 0.5f); // 浅绿色表示被车辆占用
            }
            else
            {
                backgroundColor = GetColorForPosType(cellData.posType);
            }
            EditorGUI.DrawRect(rect, backgroundColor);

            // 绘制边框
            Color borderColor = isCarOccupied ? Color.green : Color.black;
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, 1), borderColor);
            EditorGUI.DrawRect(new Rect(rect.x, rect.y + rect.height - 1, rect.width, 1), borderColor);
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, 1, rect.height), borderColor);
            EditorGUI.DrawRect(new Rect(rect.x + rect.width - 1, rect.y, 1, rect.height), borderColor);

            // 绘制文本
            GUIStyle style = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleCenter,
                fontSize = 10,
                normal = { textColor = Color.black }
            };

            string displayText;
            if (isCarOccupied)
            {
                displayText = "车体"; // 显示这是车辆占用的格子
            }
            else
            {
                displayText = GetDisplayTextForPosType(cellData.posType);
                if (cellData.posType == JamPosType.Car)
                {
                    displayText = "车头\n" + displayText + $"\nL:{cellData.length}\n{GetDirectionSymbol(cellData.carDirection)}";
                }
            }

            GUI.Label(rect, displayText, style);

            // 处理鼠标事件
            Event currentEvent = Event.current;
            if (currentEvent.type == EventType.MouseDown && rect.Contains(currentEvent.mousePosition))
            {
                if (isCarOccupied)
                {
                    // 如果点击的是车辆占用的格子，提示用户点击车头进行编辑
                    Debug.Log($"位置 ({row}, {col}) 被车辆占用，请点击车头位置进行编辑");
                }
                else
                {
                    if (currentEvent.button == 0) // 左键
                    {
                        SetCell(row, col);
                    }
                    else if (currentEvent.button == 1) // 右键
                    {
                        ClearCell(row, col);
                    }
                }
                currentEvent.Use();
                Repaint();
            }
        }

        private void SetCell(int row, int col)
        {
            if (selectedPosType == JamPosType.Car)
            {
                // 验证车辆是否可以放置在这个位置
                if (!CanPlaceCarAt(row, col, selectedDirection, selectedLength, out string errorMessage))
                {
                    Debug.LogWarning($"无法在位置 ({row}, {col}) 放置车辆: {errorMessage}");
                    return;
                }

                // 清除车辆将要占用的所有格子
                ClearCarOccupiedCells(row, col, selectedDirection, selectedLength);
            }

            var newData = new JamPosData
            {
                posType = selectedPosType,
                carDirection = selectedDirection,
                length = selectedLength
            };
            currentConfig.map[row, col] = newData;
            EditorUtility.SetDirty(currentConfig);
        }

        private void ClearCell(int row, int col)
        {
            // 如果清除的是车头，需要清除整个车辆占用的区域
            var cellData = currentConfig.map[row, col];
            if (cellData.posType == JamPosType.Car)
            {
                ClearCarOccupiedCells(row, col, cellData.carDirection, cellData.length);
            }

            currentConfig.map[row, col] = new JamPosData { posType = JamPosType.Empty };
            EditorUtility.SetDirty(currentConfig);
        }

        /// <summary>
        /// 检查是否可以在指定位置放置车辆
        /// </summary>
        private bool CanPlaceCarAt(int headRow, int headCol, JamDirection direction, int length, out string errorMessage)
        {
            errorMessage = "";

            // 检查车头位置是否在地图范围内
            if (headRow < 0 || headRow >= currentConfig.rows || headCol < 0 || headCol >= currentConfig.cols)
            {
                errorMessage = "车头位置超出地图范围";
                return false;
            }

            // 获取车辆占用的所有格子位置
            var occupiedCells = GetCarOccupiedCells(headRow, headCol, direction, length);

            // 检查所有占用的格子是否在地图范围内
            foreach (var (row, col) in occupiedCells)
            {
                if (row < 0 || row >= currentConfig.rows || col < 0 || col >= currentConfig.cols)
                {
                    errorMessage = $"车辆长度 {length} 超出地图边界";
                    return false;
                }

                // 检查格子是否被其他车辆或障碍物占用
                var cellData = currentConfig.map[row, col];
                if (cellData.posType == JamPosType.Lock)
                {
                    errorMessage = $"位置 ({row}, {col}) 被锁定，无法放置车辆";
                    return false;
                }

                // 如果格子已经有车辆，且不是当前要放置的车头位置，则不能放置
                if (cellData.posType == JamPosType.Car && !(row == headRow && col == headCol))
                {
                    errorMessage = $"位置 ({row}, {col}) 已被其他车辆占用";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取车辆占用的所有格子位置（包括车头）
        /// </summary>
        private System.Collections.Generic.List<(int row, int col)> GetCarOccupiedCells(int headRow, int headCol, JamDirection direction, int length)
        {
            var cells = new System.Collections.Generic.List<(int, int)>();

            for (int i = 0; i < length; i++)
            {
                int row = headRow;
                int col = headCol;

                // 根据方向计算每个格子的位置
                switch (direction)
                {
                    case JamDirection.Up:
                        row = headRow - i;
                        break;
                    case JamDirection.Down:
                        row = headRow + i;
                        break;
                    case JamDirection.Left:
                        col = headCol - i;
                        break;
                    case JamDirection.Right:
                        col = headCol + i;
                        break;
                }

                cells.Add((row, col));
            }

            return cells;
        }

        /// <summary>
        /// 清除车辆占用的所有格子（除了车头）
        /// </summary>
        private void ClearCarOccupiedCells(int headRow, int headCol, JamDirection direction, int length)
        {
            var occupiedCells = GetCarOccupiedCells(headRow, headCol, direction, length);

            // 清除除车头外的所有占用格子
            for (int i = 1; i < occupiedCells.Count; i++)
            {
                var (row, col) = occupiedCells[i];
                if (row >= 0 && row < currentConfig.rows && col >= 0 && col < currentConfig.cols)
                {
                    currentConfig.map[row, col] = new JamPosData { posType = JamPosType.Empty };
                }
            }
        }

        /// <summary>
        /// 检查指定格子是否被车辆占用（但不是车头）
        /// </summary>
        private bool IsCarOccupiedCell(int checkRow, int checkCol)
        {
            // 遍历地图中的所有车头
            for (int row = 0; row < currentConfig.rows; row++)
            {
                for (int col = 0; col < currentConfig.cols; col++)
                {
                    var cellData = currentConfig.map[row, col];
                    if (cellData.posType == JamPosType.Car)
                    {
                        // 获取这个车辆占用的所有格子
                        var occupiedCells = GetCarOccupiedCells(row, col, cellData.carDirection, cellData.length);

                        // 检查目标格子是否在占用列表中（但不是车头）
                        for (int i = 1; i < occupiedCells.Count; i++) // 从1开始，跳过车头
                        {
                            var (occupiedRow, occupiedCol) = occupiedCells[i];
                            if (occupiedRow == checkRow && occupiedCol == checkCol)
                            {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }

        private void ResizeMap()
        {
            var oldMap = currentConfig.map;
            int oldRows = oldMap?.GetLength(0) ?? 0;
            int oldCols = oldMap?.GetLength(1) ?? 0;

            currentConfig.map = new JamPosData[currentConfig.rows, currentConfig.cols];

            if (oldMap != null)
            {
                for (int i = 0; i < Mathf.Min(currentConfig.rows, oldRows); i++)
                {
                    for (int j = 0; j < Mathf.Min(currentConfig.cols, oldCols); j++)
                    {
                        currentConfig.map[i, j] = oldMap[i, j];
                    }
                }
            }

            for (int i = 0; i < currentConfig.rows; i++)
            {
                for (int j = 0; j < currentConfig.cols; j++)
                {
                    if (i >= oldRows || j >= oldCols)
                    {
                        currentConfig.map[i, j] = new JamPosData { posType = JamPosType.Empty };
                    }
                }
            }

            EditorUtility.SetDirty(currentConfig);
        }

        private void ClearMap()
        {
            if (currentConfig.map != null)
            {
                for (int i = 0; i < currentConfig.rows; i++)
                {
                    for (int j = 0; j < currentConfig.cols; j++)
                    {
                        currentConfig.map[i, j] = new JamPosData { posType = JamPosType.Empty };
                    }
                }
                EditorUtility.SetDirty(currentConfig);
            }
        }

        private void FillEmpty()
        {
            if (currentConfig.map != null)
            {
                for (int i = 0; i < currentConfig.rows; i++)
                {
                    for (int j = 0; j < currentConfig.cols; j++)
                    {
                        if (currentConfig.map[i, j].posType == JamPosType.Lock)
                            currentConfig.map[i, j] = new JamPosData { posType = JamPosType.Empty };
                    }
                }
                EditorUtility.SetDirty(currentConfig);
            }
        }

        private void RandomGenerate()
        {
            if (currentConfig.map != null)
            {
                for (int i = 0; i < currentConfig.rows; i++)
                {
                    for (int j = 0; j < currentConfig.cols; j++)
                    {
                        var randomType = (JamPosType)Random.Range(-1, 3);
                        var randomDirection = (JamDirection)Random.Range(0, 4);
                        var randomLength = Random.Range(1, 4);

                        currentConfig.map[i, j] = new JamPosData
                        {
                            posType = randomType,
                            carDirection = randomDirection,
                            length = randomLength
                        };
                    }
                }
                EditorUtility.SetDirty(currentConfig);
            }
        }

        private Color GetColorForPosType(JamPosType posType)
        {
            return posType switch
            {
                JamPosType.Empty => Color.white,
                JamPosType.Car => Color.green,
                JamPosType.Lock => Color.red,
                JamPosType.Undergroud => Color.blue,
                _ => Color.gray
            };
        }

        private string GetDisplayTextForPosType(JamPosType posType)
        {
            return posType switch
            {
                JamPosType.Empty => "空",
                JamPosType.Car => "车",
                JamPosType.Lock => "锁",
                JamPosType.Undergroud => "地下",
                _ => "?"
            };
        }

        private string GetDirectionSymbol(JamDirection direction)
        {
            return direction switch
            {
                JamDirection.Up => "↑",
                JamDirection.Down => "↓",
                JamDirection.Left => "←",
                JamDirection.Right => "→",
                _ => "?"
            };
        }

        private string GetDirectionName(JamDirection direction)
        {
            return direction switch
            {
                JamDirection.Up => "向上",
                JamDirection.Down => "向下",
                JamDirection.Left => "向左",
                JamDirection.Right => "向右",
                _ => "未知"
            };
        }

        private void ExportToJson()
        {
            string path = EditorUtility.SaveFilePanel("导出关卡配置", Application.dataPath, currentConfig.name + ".json", "json");
            if (!string.IsNullOrEmpty(path))
            {
                var levelData = new JamLevelData
                {
                    levelTarget = currentConfig.levelTarget,
                    rows = currentConfig.rows,
                    cols = currentConfig.cols,
                    mapData = new JamPosData[currentConfig.rows * currentConfig.cols]
                };

                // 将二维数组转换为一维数组
                for (int i = 0; i < currentConfig.rows; i++)
                {
                    for (int j = 0; j < currentConfig.cols; j++)
                    {
                        levelData.mapData[i * currentConfig.cols + j] = currentConfig.map[i, j];
                    }
                }

                string json = JsonUtility.ToJson(levelData, true);
                File.WriteAllText(path, json);
                Debug.Log($"关卡配置已导出到: {path}");
            }
        }

        private void ImportFromJson()
        {
            string path = EditorUtility.OpenFilePanel("导入关卡配置", Application.dataPath, "json");
            if (!string.IsNullOrEmpty(path))
            {
                try
                {
                    string json = File.ReadAllText(path);
                    var levelData = JsonUtility.FromJson<JamLevelData>(json);

                    currentConfig.levelTarget = levelData.levelTarget;
                    currentConfig.rows = levelData.rows;
                    currentConfig.cols = levelData.cols;

                    ResizeMap();

                    // 将一维数组转换为二维数组
                    for (int i = 0; i < currentConfig.rows; i++)
                    {
                        for (int j = 0; j < currentConfig.cols; j++)
                        {
                            if (i * currentConfig.cols + j < levelData.mapData.Length)
                            {
                                currentConfig.map[i, j] = levelData.mapData[i * currentConfig.cols + j];
                            }
                        }
                    }

                    EditorUtility.SetDirty(currentConfig);
                    Debug.Log($"关卡配置已从 {path} 导入");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"导入失败: {e.Message}");
                }
            }
        }

        private void ApplyTemplate()
        {
            if (selectedTemplate != null && currentConfig != null)
            {
                selectedTemplate.ApplyTemplateToConfig(currentConfig);
                EditorUtility.SetDirty(currentConfig);
                Debug.Log($"模板 {selectedTemplate.templateName} 已应用到当前配置");
            }
        }

        private void CreateNewTemplate()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建新的关卡模板",
                "NewJamTemplate",
                "asset",
                "选择保存位置");

            if (!string.IsNullOrEmpty(path))
            {
                var template = CreateInstance<JamLevelTemplate>();

                // 如果当前有配置，将其作为模板的基础
                if (currentConfig != null)
                {
                    template.templateName = currentConfig.name + "_Template";
                    template.levelTarget = currentConfig.levelTarget;
                    template.rows = currentConfig.rows;
                    template.cols = currentConfig.cols;

                    if (currentConfig.map != null)
                    {
                        template.templateMap = new JamPosData[currentConfig.rows, currentConfig.cols];
                        for (int i = 0; i < currentConfig.rows; i++)
                        {
                            for (int j = 0; j < currentConfig.cols; j++)
                            {
                                template.templateMap[i, j] = currentConfig.map[i, j];
                            }
                        }
                    }
                }

                AssetDatabase.CreateAsset(template, path);
                AssetDatabase.SaveAssets();
                selectedTemplate = template;
                Debug.Log($"新模板已创建: {path}");
            }
        }
    }
}
